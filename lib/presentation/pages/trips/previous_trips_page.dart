import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../data/models/previous_trip_model.dart';
import '../../../domain/entities/trip.dart';
import '../../controllers/trip_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/custom_error_widget.dart';
import '../../widgets/trips/previous_trips_table.dart';
import '../../widgets/trips/previous_trips_filters_widget.dart';

/// PreviousTripsPage for displaying and managing previous trips
/// Following Single Responsibility Principle by focusing only on previous trips display
class PreviousTripsPage extends StatefulWidget {
  const PreviousTripsPage({super.key});

  @override
  State<PreviousTripsPage> createState() => _PreviousTripsPageState();
}

class _PreviousTripsPageState extends State<PreviousTripsPage> {
  late TripController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<TripController>();
    // Load recent trips when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadRecentTrips();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.background,
      body: ResponsiveSidebar(
        child: GetBuilder<TripController>(
          builder: (controller) {
            return RefreshIndicator(
              onRefresh: controller.refreshRecentTrips,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Page header
                    _buildPageHeader(context),

                    const SizedBox(height: 24),

                    // Filters
                    const PreviousTripsFiltersWidget(),

                    const SizedBox(height: 24),

                    // Previous trips table
                    _buildTripsTable(controller),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPageHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            ColorConstants.secondary,
            ColorConstants.secondary.withAlpha(204), // 0.8 * 255 = 204
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.secondary.withAlpha(77), // 0.3 * 255 = 77
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.history_rounded,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الرحلات السابقة',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'عرض وإدارة سجل الرحلات المكتملة',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withAlpha(230), // 0.9 * 255 = 230
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: controller.refreshRecentTrips,
            icon: const Icon(Icons.refresh_rounded, color: Colors.white),
            tooltip: 'تحديث',
          ),
        ],
      ),
    );
  }

  Widget _buildTripsTable(TripController controller) {
    final isDesktop = ResponsiveUtils.isDesktop(Get.context!);
    final screenHeight = MediaQuery.of(Get.context!).size.height;
    final tableHeight = isDesktop ? screenHeight - 300 : screenHeight - 350;

    return Container(
      height: tableHeight,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 = 13
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.table_chart_rounded,
                  color: Get.theme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'سجل الرحلات',
                  style: Get.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Obx(
                  () => Text(
                    '${controller.recentTrips.length} رحلة',
                    style: Get.textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Table content
          Expanded(
            child: Obx(() {
              if (controller.isLoading && controller.recentTrips.isEmpty) {
                return const LoadingWidget(
                  message: 'جاري تحميل الرحلات السابقة...',
                );
              }

              if (controller.errorMessage.isNotEmpty &&
                  controller.recentTrips.isEmpty) {
                return CustomErrorWidget(
                  message: controller.errorMessage,
                  onRetry: controller.refreshRecentTrips,
                );
              }

              if (controller.recentTrips.isEmpty) {
                return _buildEmptyState();
              }

              return PreviousTripsTable(
                trips: _convertTripsToPreviewTrips(controller.recentTrips),
                isLoading: controller.isLoading,
                onTripTap:
                    (trip) => _viewTripDetails(trip.id?.toString() ?? ''),
                onLoadMore: () => controller.loadMoreRecentTrips(),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history_toggle_off_rounded,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد رحلات سابقة',
            style: Get.textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أي رحلات مكتملة',
            style: Get.textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _viewTripDetails(String tripId) {
    Get.toNamed('/previous-trips/details', arguments: {'tripId': tripId});
  }

  /// Convert Trip entities to PreviousTripModel for display
  /// This is a temporary solution until we have proper previous trips models
  List<PreviousTripModel> _convertTripsToPreviewTrips(List<Trip> trips) {
    return trips
        .map(
          (trip) => PreviousTripModel(
            id: int.tryParse(trip.id),
            schoolId: int.tryParse(trip.schoolId),
            busId: int.tryParse(trip.busId),
            tripsDate: trip.startTime.toIso8601String(),
            tripType:
                trip.name.contains('صباح') || trip.name.contains('morning')
                    ? 'start_day'
                    : 'end_day',
            status: trip.status == 'completed' ? 1 : 0,
            latitude: null,
            longitude: null,
            attendanceType: null,
            endAt: trip.endTime?.toIso8601String(),
            createdAt: trip.startTime.toIso8601String(),
            updatedAt: trip.endTime?.toIso8601String(),
            bus: BusInfo(id: int.tryParse(trip.busId), name: trip.busNumber),
            attendants: [
              if (trip.driverName.isNotEmpty)
                Attendant(
                  id: int.tryParse(trip.driverId),
                  type: 'drivers',
                  name: trip.driverName,
                  logoPath: null,
                  pivot: null,
                ),
              if (trip.supervisorName.isNotEmpty)
                Attendant(
                  id: int.tryParse(trip.supervisorId),
                  type: 'admins',
                  name: trip.supervisorName,
                  logoPath: null,
                  pivot: null,
                ),
            ],
            routes: [],
            busName: trip.busNumber.isNotEmpty ? trip.busNumber : 'غير محدد',
            startTime:
                '${trip.startTime.hour.toString().padLeft(2, '0')}:${trip.startTime.minute.toString().padLeft(2, '0')}',
            endTime:
                trip.endTime != null
                    ? '${trip.endTime!.hour.toString().padLeft(2, '0')}:${trip.endTime!.minute.toString().padLeft(2, '0')}'
                    : null,
            supervisorName:
                trip.supervisorName.isNotEmpty
                    ? trip.supervisorName
                    : 'غير محدد',
            date:
                '${trip.startTime.day}/${trip.startTime.month}/${trip.startTime.year}',
            supervisorId: int.tryParse(trip.supervisorId),
          ),
        )
        .toList();
  }
}
