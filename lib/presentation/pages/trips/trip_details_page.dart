import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/trip_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../widgets/trips/trip_info_card.dart';
import '../../widgets/trips/trip_students_list.dart';
import '../../widgets/trips/trip_route_map.dart';

/// TripDetailsPage for displaying detailed trip information
/// Following Single Responsibility Principle by focusing only on trip details display
class TripDetailsPage extends StatelessWidget {
  const TripDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final String? tripId = Get.arguments?['tripId'];
    
    if (tripId == null) {
      return Scaffold(
        body: const Center(
          child: Text('معرف الرحلة غير صحيح'),
        ),
      );
    }

    return Scaffold(
      backgroundColor: ColorConstants.background,
      body: ResponsiveSidebar(
        child: GetBuilder<TripController>(
          initState: (_) {
            // Load trip details when page initializes
            WidgetsBinding.instance.addPostFrameCallback((_) {
              Get.find<TripController>().loadTripById(tripId);
            });
          },
          builder: (controller) {
            return RefreshIndicator(
              onRefresh: () => controller.loadTripById(tripId),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Page header
                    _buildPageHeader(context, tripId),
                    
                    const SizedBox(height: 24),
                    
                    // Trip content
                    _buildTripContent(controller),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPageHeader(BuildContext context, String tripId) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            ColorConstants.accent1,
            ColorConstants.accent1.withAlpha(204), // 0.8 * 255 = 204
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.accent1.withAlpha(77), // 0.3 * 255 = 77
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.info_outline_rounded,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تفاصيل الرحلة',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'معرف الرحلة: $tripId',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withAlpha(230), // 0.9 * 255 = 230
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: () => Get.find<TripController>().loadTripById(tripId),
                icon: const Icon(
                  Icons.refresh_rounded,
                  color: Colors.white,
                ),
                tooltip: 'تحديث',
              ),
              IconButton(
                onPressed: () => Get.back(),
                icon: const Icon(
                  Icons.arrow_back_rounded,
                  color: Colors.white,
                ),
                tooltip: 'العودة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTripContent(TripController controller) {
    return Obx(() {
      if (controller.isLoading) {
        return const LoadingWidget(message: 'جاري تحميل تفاصيل الرحلة...');
      }
      
      if (controller.errorMessage.isNotEmpty) {
        return CustomErrorWidget(
          message: controller.errorMessage,
          onRetry: () => controller.loadTripById(Get.arguments?['tripId']),
        );
      }
      
      if (controller.selectedTrip == null) {
        return _buildTripNotFound();
      }
      
      final trip = controller.selectedTrip!;
      final isDesktop = ResponsiveUtils.isDesktop(Get.context!);
      
      if (isDesktop) {
        return _buildDesktopLayout(trip);
      } else {
        return _buildMobileLayout(trip);
      }
    });
  }

  Widget _buildTripNotFound() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off_rounded,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'الرحلة غير موجودة',
            style: Get.textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على الرحلة المطلوبة',
            style: Get.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.arrow_back_rounded),
            label: const Text('العودة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Get.theme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(trip) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column - Trip info and students
        Expanded(
          flex: 2,
          child: Column(
            children: [
              TripInfoCard(trip: trip),
              const SizedBox(height: 24),
              TripStudentsList(trip: trip),
            ],
          ),
        ),
        
        const SizedBox(width: 24),
        
        // Right column - Map and route
        Expanded(
          flex: 3,
          child: TripRouteMap(trip: trip),
        ),
      ],
    );
  }

  Widget _buildMobileLayout(trip) {
    return Column(
      children: [
        TripInfoCard(trip: trip),
        const SizedBox(height: 24),
        TripRouteMap(trip: trip),
        const SizedBox(height: 24),
        TripStudentsList(trip: trip),
      ],
    );
  }
}
