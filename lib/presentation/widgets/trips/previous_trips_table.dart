import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/previous_trip_model.dart';
import '../../../core/utils/responsive_utils.dart';

/// PreviousTripsTable widget for displaying previous trips in a table format
/// Following Single Responsibility Principle by focusing only on trips table display
class PreviousTripsTable extends StatelessWidget {
  final List<PreviousTripModel> trips;
  final bool isLoading;
  final Function(PreviousTripModel) onTripTap;
  final VoidCallback onLoadMore;

  const PreviousTripsTable({
    super.key,
    required this.trips,
    required this.isLoading,
    required this.onTripTap,
    required this.onLoadMore,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    if (isDesktop) {
      return _buildDesktopTable();
    } else {
      return _buildMobileList();
    }
  }

  Widget _buildDesktopTable() {
    return Column(
      children: [
        // Table header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
          ),
          child: Row(
            children: [
              const Expanded(
                flex: 2,
                child: Text(
                  'اسم الرحلة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 2,
                child: Text(
                  'الباص',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 2,
                child: Text(
                  'السائق',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 2,
                child: Text(
                  'التاريخ',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 1,
                child: Text(
                  'الحالة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 1,
                child: Text(
                  'الطلاب',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(width: 60), // Actions column
            ],
          ),
        ),

        // Table rows
        Expanded(
          child: ListView.builder(
            itemCount: trips.length + (isLoading ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == trips.length) {
                // Loading indicator at the end
                return const Padding(
                  padding: EdgeInsets.all(16),
                  child: Center(child: CircularProgressIndicator()),
                );
              }

              final trip = trips[index];
              return _buildDesktopTableRow(trip, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopTableRow(PreviousTripModel trip, int index) {
    return InkWell(
      onTap: () => onTripTap(trip),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: index.isEven ? Colors.white : Colors.grey[50],
          border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                trip.tripType == 'start_day' ? 'رحلة الصباح' : 'رحلة المساء',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
            Expanded(flex: 2, child: Text(trip.busName ?? 'غير محدد')),
            Expanded(flex: 2, child: Text(trip.supervisorName ?? 'غير محدد')),
            Expanded(flex: 2, child: Text(trip.date ?? 'غير محدد')),
            Expanded(flex: 1, child: _buildStatusChip(trip.status ?? 0)),
            Expanded(flex: 1, child: Text('${trip.attendants?.length ?? 0}')),
            SizedBox(
              width: 60,
              child: IconButton(
                onPressed: () => onTripTap(trip),
                icon: const Icon(Icons.visibility_rounded),
                tooltip: 'عرض التفاصيل',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileList() {
    return ListView.builder(
      itemCount: trips.length + (isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == trips.length) {
          // Loading indicator at the end
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final trip = trips[index];
        return _buildMobileCard(trip);
      },
    );
  }

  Widget _buildMobileCard(PreviousTripModel trip) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => onTripTap(trip),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Trip name and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      trip.tripType == 'start_day'
                          ? 'رحلة الصباح'
                          : 'رحلة المساء',
                      style: Get.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildStatusChip(trip.status ?? 0),
                ],
              ),

              const SizedBox(height: 12),

              // Trip details
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.directions_bus_rounded,
                      label: 'الباص',
                      value: trip.busName ?? 'غير محدد',
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.person_rounded,
                      label: 'المشرف',
                      value: trip.supervisorName ?? 'غير محدد',
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.calendar_today_rounded,
                      label: 'التاريخ',
                      value: trip.date ?? 'غير محدد',
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.people_rounded,
                      label: 'المرافقين',
                      value: '${trip.attendants?.length ?? 0}',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: Get.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(int status) {
    Color backgroundColor;
    Color textColor;
    String statusText;

    switch (status) {
      case 1:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[800]!;
        statusText = 'مكتملة';
        break;
      case 0:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[800]!;
        statusText = 'ملغية';
        break;
      default:
        backgroundColor = Colors.grey[100]!;
        textColor = Colors.grey[800]!;
        statusText = 'غير معروف';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
