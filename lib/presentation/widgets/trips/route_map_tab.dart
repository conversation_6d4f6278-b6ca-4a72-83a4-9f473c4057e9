import 'package:flutter/material.dart';
import '../../../data/models/route_model.dart';
import '../../../data/models/student_attendance_model.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../common/loading_widget.dart';
import 'trip_route_map.dart';

/// Route Map Tab Widget
/// Displays trip route on map with route points
/// Based on the original SchoolX project structure
class RouteMapTab extends StatefulWidget {
  final TripRouteModel? routeData;
  final List<StudentAttendanceModel>? presentStudents;
  final List<StudentAttendanceModel>? absentStudents;
  final bool isLoading;
  final VoidCallback onRefresh;

  const RouteMapTab({
    super.key,
    required this.routeData,
    this.presentStudents,
    this.absentStudents,
    required this.isLoading,
    required this.onRefresh,
  });

  @override
  State<RouteMapTab> createState() => _RouteMapTabState();
}

class _RouteMapTabState extends State<RouteMapTab> {
  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return const LoadingWidget(message: 'جاري تحميل بيانات المسار...');
    }

    if (widget.routeData == null ||
        widget.routeData!.routePoints == null ||
        widget.routeData!.routePoints!.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async => widget.onRefresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            // Route statistics
            _buildRouteStatistics(),

            // Map placeholder (since we don't have Google Maps in this project)
            _buildMapPlaceholder(),

            // Route points list
            _buildRoutePointsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.map_outlined, size: 80, color: Colors.grey[400]),
            const SizedBox(height: 24),
            Text(
              'لا توجد بيانات مسار',
              style: TextStyle(
                fontSize: ResponsiveUtils.getFontSize(18),
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على بيانات المسار لهذه الرحلة',
              style: TextStyle(
                fontSize: ResponsiveUtils.getFontSize(14),
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: widget.onRefresh,
              icon: const Icon(Icons.refresh_rounded, color: Colors.white),
              label: Text(
                'إعادة المحاولة',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: ResponsiveUtils.getFontSize(14),
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primary,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRouteStatistics() {
    final routeData = widget.routeData!;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات المسار',
            style: TextStyle(
              fontSize: ResponsiveUtils.getFontSize(16),
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.route,
                  label: 'نقاط المسار',
                  value: '${routeData.routePoints?.length ?? 0}',
                  color: ColorConstants.primary,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.straighten,
                  label: 'المسافة الإجمالية',
                  value:
                      '${(routeData.totalDistance ?? 0).toStringAsFixed(1)} كم',
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.access_time,
                  label: 'الوقت المقدر',
                  value: '${routeData.estimatedTime ?? 0} دقيقة',
                  color: Colors.green,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.timeline,
                  label: 'حالة المسار',
                  value: 'مكتمل',
                  color: Colors.blue,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: ResponsiveUtils.getFontSize(14),
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: ResponsiveUtils.getFontSize(12),
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMapPlaceholder() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TripRouteMap(
        routeData: widget.routeData,
        presentStudents: widget.presentStudents,
        absentStudents: widget.absentStudents,
        height: 300,
      ),
    );
  }

  Widget _buildRoutePointsList() {
    final routePoints = widget.routeData!.routePoints!;

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'نقاط المسار (${routePoints.length})',
              style: TextStyle(
                fontSize: ResponsiveUtils.getFontSize(16),
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: routePoints.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final point = routePoints[index];
              final isFirst = index == 0;
              final isLast = index == routePoints.length - 1;

              return ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        isFirst
                            ? Colors.green
                            : isLast
                            ? Colors.red
                            : ColorConstants.primary,
                  ),
                  child: Center(
                    child: Icon(
                      isFirst
                          ? Icons.play_arrow
                          : isLast
                          ? Icons.stop
                          : Icons.location_on,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
                title: Text(
                  isFirst
                      ? 'نقطة البداية'
                      : isLast
                      ? 'نقطة النهاية'
                      : 'نقطة ${index + 1}',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getFontSize(14),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  'خط العرض: ${point.latitude ?? 'غير محدد'}\nخط الطول: ${point.longitude ?? 'غير محدد'}',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getFontSize(12),
                    color: Colors.grey[600],
                  ),
                ),
                trailing:
                    point.createdAt != null
                        ? Text(
                          _formatTime(point.createdAt!),
                          style: TextStyle(
                            fontSize: ResponsiveUtils.getFontSize(12),
                            color: Colors.grey[500],
                          ),
                        )
                        : null,
              );
            },
          ),
        ],
      ),
    );
  }

  String _formatTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'غير محدد';
    }
  }
}
