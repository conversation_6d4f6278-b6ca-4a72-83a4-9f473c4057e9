import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../../data/models/route_model.dart';
import '../../../data/models/student_attendance_model.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import 'student_map_markers.dart';
import 'mock_trip_route_map.dart';

/// TripRouteMap widget for displaying trip route and location tracking
/// Following Single Responsibility Principle by focusing only on map display
/// Integrated with Google Maps for real route display
class TripRouteMap extends StatefulWidget {
  final TripRouteModel? routeData;
  final List<StudentAttendanceModel>? presentStudents;
  final List<StudentAttendanceModel>? absentStudents;
  final double height;
  final VoidCallback? onMapCreated;

  const TripRouteMap({
    super.key,
    required this.routeData,
    this.presentStudents,
    this.absentStudents,
    this.height = 400,
    this.onMapCreated,
  });

  @override
  State<TripRouteMap> createState() => _TripRouteMapState();
}

class _TripRouteMapState extends State<TripRouteMap> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};
  final Set<Polyline> _polylines = {};
  LatLngBounds? _bounds;
  bool _hasMapError = false;

  @override
  void initState() {
    super.initState();
    _setupMapData();
  }

  @override
  void didUpdateWidget(TripRouteMap oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.routeData != widget.routeData) {
      _setupMapData();
    }
  }

  void _setupMapData() {
    if (widget.routeData?.routePoints == null ||
        widget.routeData!.routePoints!.isEmpty) {
      return;
    }

    final routePoints = widget.routeData!.routePoints!;
    final latLngPoints = <LatLng>[];

    // Convert route points to LatLng
    for (final point in routePoints) {
      if (point.latitude != null && point.longitude != null) {
        try {
          final lat = double.parse(point.latitude!);
          final lng = double.parse(point.longitude!);
          latLngPoints.add(LatLng(lat, lng));
        } catch (e) {
          // Skip invalid coordinates
          continue;
        }
      }
    }

    if (latLngPoints.isEmpty) return;

    // Create markers
    _createMarkers(latLngPoints, routePoints);

    // Create polyline
    _createPolyline(latLngPoints);

    // Calculate bounds
    _calculateBounds(latLngPoints);

    setState(() {});
  }

  void _createMarkers(
    List<LatLng> latLngPoints,
    List<RoutePointModel> routePoints,
  ) {
    _markers.clear();

    for (int i = 0; i < latLngPoints.length; i++) {
      final point = latLngPoints[i];
      final routePoint = routePoints[i];

      // Determine marker type
      String markerId;
      BitmapDescriptor icon;
      String infoTitle;

      if (i == 0) {
        // Start point
        markerId = 'start_point';
        icon = BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
        infoTitle = 'نقطة البداية';
      } else if (i == latLngPoints.length - 1) {
        // End point
        markerId = 'end_point';
        icon = BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
        infoTitle = 'نقطة النهاية';
      } else {
        // Intermediate point
        markerId = 'point_$i';
        icon = BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
        infoTitle = 'نقطة ${i + 1}';
      }

      _markers.add(
        Marker(
          markerId: MarkerId(markerId),
          position: point,
          icon: icon,
          infoWindow: InfoWindow(
            title: infoTitle,
            snippet:
                routePoint.createdAt != null
                    ? _formatTime(routePoint.createdAt!)
                    : null,
          ),
        ),
      );
    }

    // Add student location markers if available
    _addStudentMarkers();
  }

  void _addStudentMarkers() {
    // Add present students markers using StudentMapMarkers
    if (widget.presentStudents != null) {
      final presentMarkers = StudentMapMarkers.createClusteredMarkers(
        widget.presentStudents!,
        true,
      );
      _markers.addAll(presentMarkers);
    }

    // Add absent students markers using StudentMapMarkers
    if (widget.absentStudents != null) {
      final absentMarkers = StudentMapMarkers.createClusteredMarkers(
        widget.absentStudents!,
        false,
      );
      _markers.addAll(absentMarkers);
    }
  }

  void _createPolyline(List<LatLng> latLngPoints) {
    _polylines.clear();

    if (latLngPoints.length > 1) {
      _polylines.add(
        Polyline(
          polylineId: const PolylineId('trip_route'),
          points: latLngPoints,
          color: ColorConstants.primary,
          width: 4,
          patterns: [], // Solid line
        ),
      );
    }
  }

  void _calculateBounds(List<LatLng> latLngPoints) {
    final allPoints = <LatLng>[...latLngPoints];

    // Add student locations to bounds calculation
    final allStudents = <StudentAttendanceModel>[];
    if (widget.presentStudents != null) {
      allStudents.addAll(widget.presentStudents!);
    }
    if (widget.absentStudents != null) {
      allStudents.addAll(widget.absentStudents!);
    }

    // Add student coordinates to bounds
    for (final student in allStudents) {
      if (student.latitude != null && student.longitude != null) {
        try {
          final lat = double.parse(student.latitude!);
          final lng = double.parse(student.longitude!);
          allPoints.add(LatLng(lat, lng));
        } catch (e) {
          // Skip invalid coordinates
        }
      }
    }

    if (allPoints.isEmpty) return;

    double minLat = allPoints.first.latitude;
    double maxLat = allPoints.first.latitude;
    double minLng = allPoints.first.longitude;
    double maxLng = allPoints.first.longitude;

    for (final point in allPoints) {
      minLat = minLat < point.latitude ? minLat : point.latitude;
      maxLat = maxLat > point.latitude ? maxLat : point.latitude;
      minLng = minLng < point.longitude ? minLng : point.longitude;
      maxLng = maxLng > point.longitude ? maxLng : point.longitude;
    }

    _bounds = LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  bool _shouldUseMockMap() {
    // Use Google Maps now that we have a valid API key
    // Only use mock map if there are errors
    return false;
  }

  void _onMapCreated(GoogleMapController controller) {
    try {
      _mapController = controller;

      // Fit the map to show all route points
      if (_bounds != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngBounds(_bounds!, 50.0),
        );
      }

      widget.onMapCreated?.call();
    } catch (e) {
      // If map creation fails, switch to mock map
      setState(() {
        _hasMapError = true;
      });
    }
  }

  String _formatTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.routeData?.routePoints == null ||
        widget.routeData!.routePoints!.isEmpty) {
      return _buildEmptyState();
    }

    // Use MockTripRouteMap as fallback for web or when Google Maps fails
    if (_hasMapError || _shouldUseMockMap()) {
      return MockTripRouteMap(
        routeData: widget.routeData,
        presentStudents: widget.presentStudents,
        absentStudents: widget.absentStudents,
        height: widget.height,
        onMapCreated: widget.onMapCreated,
      );
    }

    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header with stats
          _buildHeader(),

          // Google Map
          Expanded(child: _buildGoogleMap()),

          // Route info
          _buildRouteInfo(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorConstants.primary.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.map_rounded, color: ColorConstants.primary, size: 24),
          const SizedBox(width: 8),
          Text(
            'خريطة المسار',
            style: TextStyle(
              fontSize: ResponsiveUtils.getFontSize(16),
              fontWeight: FontWeight.bold,
              color: ColorConstants.primary,
            ),
          ),
          const Spacer(),
          _buildLegend(),
        ],
      ),
    );
  }

  Widget _buildLegend() {
    return Wrap(
      spacing: 8,
      children: [
        _buildLegendItem(
          color: Colors.green,
          label: 'بداية',
          icon: Icons.play_arrow,
        ),
        _buildLegendItem(
          color: Colors.blue,
          label: 'مسار',
          icon: Icons.location_on,
        ),
        _buildLegendItem(color: Colors.red, label: 'نهاية', icon: Icons.stop),
        if (widget.presentStudents != null &&
            widget.presentStudents!.isNotEmpty)
          _buildLegendItem(
            color: Colors.green,
            label: 'حاضر',
            icon: Icons.person,
          ),
        if (widget.absentStudents != null && widget.absentStudents!.isNotEmpty)
          _buildLegendItem(
            color: Colors.orange,
            label: 'غائب',
            icon: Icons.person_off,
          ),
      ],
    );
  }

  Widget _buildLegendItem({
    required Color color,
    required String label,
    required IconData icon,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: ResponsiveUtils.getFontSize(12),
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildGoogleMap() {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        bottomLeft: Radius.circular(16),
        bottomRight: Radius.circular(16),
      ),
      child: GoogleMap(
        onMapCreated: _onMapCreated,
        initialCameraPosition: _getInitialCameraPosition(),
        markers: _markers,
        polylines: _polylines,
        mapType: MapType.normal,
        myLocationEnabled: false,
        myLocationButtonEnabled: false,
        zoomControlsEnabled: true,
        compassEnabled: true,
        mapToolbarEnabled: false,
        buildingsEnabled: true,
        trafficEnabled: false,
      ),
    );
  }

  CameraPosition _getInitialCameraPosition() {
    if (widget.routeData?.routePoints != null &&
        widget.routeData!.routePoints!.isNotEmpty) {
      final firstPoint = widget.routeData!.routePoints!.first;
      if (firstPoint.latitude != null && firstPoint.longitude != null) {
        try {
          final lat = double.parse(firstPoint.latitude!);
          final lng = double.parse(firstPoint.longitude!);
          return CameraPosition(target: LatLng(lat, lng), zoom: 14.0);
        } catch (e) {
          // Fall back to default position
        }
      }
    }

    // Default position (Cairo, Egypt)
    return const CameraPosition(target: LatLng(30.0444, 31.2357), zoom: 10.0);
  }

  Widget _buildRouteInfo() {
    final routeData = widget.routeData!;
    final studentStats = StudentMapMarkers.getStudentLocationStats(
      widget.presentStudents ?? [],
      widget.absentStudents ?? [],
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          // Route statistics
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildInfoItem(
                icon: Icons.route,
                label: 'نقاط المسار',
                value: '${routeData.routePoints?.length ?? 0}',
                color: ColorConstants.primary,
              ),
              _buildInfoItem(
                icon: Icons.straighten,
                label: 'المسافة',
                value:
                    '${(routeData.totalDistance ?? 0).toStringAsFixed(1)} كم',
                color: Colors.orange,
              ),
              _buildInfoItem(
                icon: Icons.access_time,
                label: 'الوقت',
                value: '${routeData.estimatedTime ?? 0} د',
                color: Colors.green,
              ),
            ],
          ),

          // Student statistics (if available)
          if (studentStats['totalStudents'] > 0) ...[
            const SizedBox(height: 12),
            const Divider(height: 1),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildInfoItem(
                  icon: Icons.people,
                  label: 'إجمالي الطلاب',
                  value: '${studentStats['totalStudents']}',
                  color: Colors.blue,
                ),
                _buildInfoItem(
                  icon: Icons.check_circle,
                  label: 'حاضرين',
                  value: '${studentStats['presentTotal']}',
                  color: Colors.green,
                ),
                _buildInfoItem(
                  icon: Icons.cancel,
                  label: 'غائبين',
                  value: '${studentStats['absentTotal']}',
                  color: Colors.red,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: ResponsiveUtils.getFontSize(14),
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: ResponsiveUtils.getFontSize(12),
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.map_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات مسار',
              style: TextStyle(
                fontSize: ResponsiveUtils.getFontSize(16),
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على نقاط المسار لهذه الرحلة',
              style: TextStyle(
                fontSize: ResponsiveUtils.getFontSize(14),
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
