import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../domain/entities/trip.dart';

/// TripRouteMap widget for displaying trip route and location tracking
/// Following Single Responsibility Principle by focusing only on map display
/// Note: This is a placeholder implementation. In production, integrate with Google Maps or similar
class TripRouteMap extends StatelessWidget {
  final Trip trip;

  const TripRouteMap({
    super.key,
    required this.trip,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 400,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26), // 0.1 * 255 = 26
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          _buildHeader(),
          
          // Map placeholder
          Expanded(
            child: _buildMapPlaceholder(),
          ),
          
          // Route info
          _buildRouteInfo(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.map_rounded,
            color: Get.theme.primaryColor,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            'خريطة الرحلة',
            style: Get.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => _openFullScreenMap(),
            icon: Icon(
              Icons.fullscreen_rounded,
              color: Colors.grey[600],
            ),
            tooltip: 'عرض بملء الشاشة',
          ),
        ],
      ),
    );
  }

  Widget _buildMapPlaceholder() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.map_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'خريطة الرحلة',
            style: Get.textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم عرض مسار الرحلة والموقع الحالي للباص هنا',
            style: Get.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildMapFeatureChip(
                icon: Icons.location_on_rounded,
                label: 'الموقع الحالي',
                color: Colors.red,
              ),
              const SizedBox(width: 8),
              _buildMapFeatureChip(
                icon: Icons.route_rounded,
                label: 'المسار',
                color: Colors.blue,
              ),
              const SizedBox(width: 8),
              _buildMapFeatureChip(
                icon: Icons.flag_rounded,
                label: 'المحطات',
                color: Colors.green,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMapFeatureChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha(26), // 0.1 * 255 = 26
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: Get.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRouteInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildRouteInfoItem(
                  icon: Icons.location_on_rounded,
                  label: 'نقطة البداية',
                  value: trip.startLocation.isNotEmpty 
                      ? trip.startLocation 
                      : 'غير محدد',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildRouteInfoItem(
                  icon: Icons.flag_rounded,
                  label: 'نقطة النهاية',
                  value: trip.endLocation.isNotEmpty 
                      ? trip.endLocation 
                      : 'غير محدد',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildRouteInfoItem(
                  icon: Icons.straighten_rounded,
                  label: 'المسافة',
                  value: trip.distance > 0 
                      ? '${trip.distance.toStringAsFixed(1)} كم'
                      : 'غير محدد',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildRouteInfoItem(
                  icon: Icons.timer_rounded,
                  label: 'المدة المتوقعة',
                  value: trip.duration > 0 
                      ? '${trip.duration} دقيقة'
                      : 'غير محدد',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRouteInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.blue[700],
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Colors.blue[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: Get.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _openFullScreenMap() {
    // TODO: Implement full screen map view
    Get.snackbar(
      'خريطة الرحلة',
      'سيتم فتح الخريطة بملء الشاشة قريباً',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.primaryColor,
      colorText: Colors.white,
    );
  }
}
